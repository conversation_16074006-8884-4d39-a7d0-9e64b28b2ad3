/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    UApp: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5.9.2__0b42327e4cc96b97ec9b947df6620b3f/node_modules/@nuxt/ui/dist/runtime/components/App.vue')['default']
  }
}
