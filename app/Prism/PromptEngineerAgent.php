<?php

namespace App\Prism;

use Prism\Prism\Prism;
use Prism\Prism\Enums\Provider;
use Prism\Prism\Text\Response as TextResponse;
use Prism\Prism\Structured\Response as StructuredResponse;
use App\Prism\Models\PromptStructure;
use App\Prism\Schema\AnalysisSchema;
use Illuminate\Contracts\View\View;

class PromptEngineerAgent
{
    private View $systemPrompt;

    public function __construct()
    {
        $this->systemPrompt = $this->buildSystemPrompt();
    }

    /**
     * Build the system prompt for the prompt engineering agent using Blade view
     */
    private function buildSystemPrompt(): View
    {
        return view('prism.system-prompt');
    }

    /**
     * Handle incoming messages and generate responses using Prism
     */
    public function handle(string $userMessage): TextResponse|string
    {
        // Validate that the request is prompt-related
        if (!$this->isPromptRelated($userMessage)) {
            // Return decline text directly for non-prompt requests
            return $this->declineNonPromptRequest();
        }

        // Use Prism to generate response with system prompt
        return Prism::text()
            ->using(Provider::Ollama, 'llama3.1')
            ->withSystemPrompt($this->systemPrompt)
            ->withPrompt($userMessage)
            ->generate();
    }

    /**
     * Handle streaming responses for real-time interaction
     */
    public function handleStreaming(string $userMessage): \Generator
    {
        if (!$this->isPromptRelated($userMessage)) {
            yield $this->declineNonPromptRequest();
            return;
        }

        $stream = Prism::text()
            ->using(Provider::Ollama, 'llama3.1')
            ->withSystemPrompt($this->systemPrompt)
            ->withPrompt($userMessage)
            ->stream();

        foreach ($stream as $chunk) {
            yield $chunk->text;
        }
    }

    /**
     * Check if the request is related to prompt engineering
     */
    private function isPromptRelated(string $content): bool
    {
        $promptKeywords = [
            'prompt',
            'prompting',
            'instruction',
            'context',
            'ai model',
            'gpt',
            'claude',
            'llm',
            'language model',
            'generate',
            'optimize',
            'refine',
            'improve',
            'template',
            'format',
            'structure'
        ];

        $contentLower = strtolower($content);

        foreach ($promptKeywords as $keyword) {
            if (str_contains($contentLower, $keyword)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Classify the type of prompt-related request
     */
    private function classifyRequest(string $content): string
    {
        $contentLower = strtolower($content);

        if (str_contains($contentLower, 'create') || str_contains($contentLower, 'generate') || str_contains($contentLower, 'write')) {
            return 'creation';
        }

        if (str_contains($contentLower, 'analyze') || str_contains($contentLower, 'review') || str_contains($contentLower, 'evaluate')) {
            return 'analysis';
        }

        if (str_contains($contentLower, 'optimize') || str_contains($contentLower, 'improve') || str_contains($contentLower, 'refine')) {
            return 'optimization';
        }

        if (str_contains($contentLower, 'learn') || str_contains($contentLower, 'teach') || str_contains($contentLower, 'explain') || str_contains($contentLower, 'how')) {
            return 'education';
        }

        if (str_contains($contentLower, 'organize') || str_contains($contentLower, 'library') || str_contains($contentLower, 'template')) {
            return 'organization';
        }

        return 'general';
    }

    /**
     * Handle non-prompt related requests
     */
    private function declineNonPromptRequest(): View
    {
        return view('prism.decline-non-prompt');
    }

    /**
     * Generate structured analysis of a prompt using Prism
     */
    public function analyzePrompt(string $prompt): StructuredResponse
    {
        return Prism::structured()
            ->using(Provider::Ollama, 'llama3.1')
            ->withSystemPrompt($this->systemPrompt . "\n\nAnalyze the provided prompt and return structured analysis data.")
            ->withSchema(AnalysisSchema::schema())
            ->withPrompt("Please analyze this prompt:\n\n{$prompt}")
            ->generate();
    }

    /**
     * Create a PromptStructure from user requirements using Prism
     */
    public function createPromptStructure(array $requirements): PromptStructure
    {
        $requirementsText = json_encode($requirements, JSON_PRETTY_PRINT);

        $response = Prism::text()
            ->using(Provider::Ollama, 'llama3.1')
            ->withSystemPrompt($this->systemPrompt . "\n\nCreate a comprehensive prompt based on the user requirements. Structure your response as a complete prompt with clear sections.")
            ->withPrompt("Create a prompt based on these requirements:\n\n{$requirementsText}")
            ->generate();

        // Parse the response and create PromptStructure
        return $this->parseResponseToPromptStructure($response->text, $requirements);
    }

    /**
     * Parse LLM response into PromptStructure
     */
    private function parseResponseToPromptStructure(string $response, array $requirements): PromptStructure
    {
        // Extract components from the markdown response
        $components = $this->extractPromptComponents($response);

        return PromptStructure::create(
            title: $requirements['title'] ?? 'Generated Prompt',
            purpose: $requirements['purpose'] ?? 'AI assistance prompt',
            components: $components,
            targetModel: $requirements['target_model'] ?? null,
            tags: $requirements['tags'] ?? []
        );
    }

    /**
     * Extract prompt components from markdown text
     */
    private function extractPromptComponents(string $markdown): \App\Prism\Models\PromptComponents
    {
        // Simple parsing logic - in a real implementation, this would be more sophisticated
        $context = $this->extractSection($markdown, 'Context');
        $role = $this->extractSection($markdown, 'Role');
        $instructions = $this->extractSection($markdown, 'Instructions') ?: $markdown;
        $outputFormat = $this->extractSection($markdown, 'Output Format');
        $constraints = $this->extractListItems($markdown, 'Constraints');
        $examples = $this->extractCodeBlocks($markdown);

        return \App\Prism\Models\PromptComponents::create(
            instructions: $instructions,
            context: $context,
            role: $role,
            outputFormat: $outputFormat,
            constraints: $constraints,
            examples: $examples
        );
    }

    /**
     * Extract a section from markdown
     */
    private function extractSection(string $markdown, string $sectionName): ?string
    {
        $pattern = "/##\s*{$sectionName}\s*\n\n(.*?)(?=\n##|\n#|$)/s";
        if (preg_match($pattern, $markdown, $matches)) {
            return trim($matches[1]);
        }
        return null;
    }

    /**
     * Extract list items from a section
     */
    private function extractListItems(string $markdown, string $sectionName): array
    {
        $section = $this->extractSection($markdown, $sectionName);
        if (!$section) {
            return [];
        }

        $lines = explode("\n", $section);
        $items = [];

        foreach ($lines as $line) {
            if (preg_match('/^-\s*(.+)$/', trim($line), $matches)) {
                $items[] = $matches[1];
            }
        }

        return $items;
    }

    /**
     * Extract code blocks from markdown
     */
    private function extractCodeBlocks(string $markdown): array
    {
        $pattern = '/```(?:\w+)?\n(.*?)\n```/s';
        preg_match_all($pattern, $markdown, $matches);
        return $matches[1] ?? [];
    }
}
