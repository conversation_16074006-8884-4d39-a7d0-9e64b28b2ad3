/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// noinspection JSUnusedGlobalSymbols
// Generated by unplugin-auto-import
// biome-ignore lint: disable
export {}
declare global {
  const avatarGroupInjectionKey: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5.9.2__2f54f49be07767b799dc398325095cac/node_modules/@nuxt/ui/dist/runtime/composables/useAvatarGroup.js')['avatarGroupInjectionKey']
  const buttonGroupInjectionKey: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5.9.2__2f54f49be07767b799dc398325095cac/node_modules/@nuxt/ui/dist/runtime/composables/useButtonGroup.js')['buttonGroupInjectionKey']
  const defineLocale: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5.9.2__2f54f49be07767b799dc398325095cac/node_modules/@nuxt/ui/dist/runtime/composables/defineLocale.js')['defineLocale']
  const defineShortcuts: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5.9.2__2f54f49be07767b799dc398325095cac/node_modules/@nuxt/ui/dist/runtime/composables/defineShortcuts.js')['defineShortcuts']
  const extendLocale: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5.9.2__2f54f49be07767b799dc398325095cac/node_modules/@nuxt/ui/dist/runtime/composables/defineLocale.js')['extendLocale']
  const extractShortcuts: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5.9.2__2f54f49be07767b799dc398325095cac/node_modules/@nuxt/ui/dist/runtime/composables/defineShortcuts.js')['extractShortcuts']
  const formBusInjectionKey: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5.9.2__2f54f49be07767b799dc398325095cac/node_modules/@nuxt/ui/dist/runtime/composables/useFormField.js')['formBusInjectionKey']
  const formFieldInjectionKey: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5.9.2__2f54f49be07767b799dc398325095cac/node_modules/@nuxt/ui/dist/runtime/composables/useFormField.js')['formFieldInjectionKey']
  const formInputsInjectionKey: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5.9.2__2f54f49be07767b799dc398325095cac/node_modules/@nuxt/ui/dist/runtime/composables/useFormField.js')['formInputsInjectionKey']
  const formLoadingInjectionKey: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5.9.2__2f54f49be07767b799dc398325095cac/node_modules/@nuxt/ui/dist/runtime/composables/useFormField.js')['formLoadingInjectionKey']
  const formOptionsInjectionKey: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5.9.2__2f54f49be07767b799dc398325095cac/node_modules/@nuxt/ui/dist/runtime/composables/useFormField.js')['formOptionsInjectionKey']
  const inputIdInjectionKey: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5.9.2__2f54f49be07767b799dc398325095cac/node_modules/@nuxt/ui/dist/runtime/composables/useFormField.js')['inputIdInjectionKey']
  const kbdKeysMap: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5.9.2__2f54f49be07767b799dc398325095cac/node_modules/@nuxt/ui/dist/runtime/composables/useKbd.js')['kbdKeysMap']
  const localeContextInjectionKey: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5.9.2__2f54f49be07767b799dc398325095cac/node_modules/@nuxt/ui/dist/runtime/composables/useLocale.js')['localeContextInjectionKey']
  const portalTargetInjectionKey: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5.9.2__2f54f49be07767b799dc398325095cac/node_modules/@nuxt/ui/dist/runtime/composables/usePortal.js')['portalTargetInjectionKey']
  const useAppConfig: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5.9.2__2f54f49be07767b799dc398325095cac/node_modules/@nuxt/ui/dist/runtime/vue/composables/useAppConfig.js')['useAppConfig']
  const useAvatarGroup: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5.9.2__2f54f49be07767b799dc398325095cac/node_modules/@nuxt/ui/dist/runtime/composables/useAvatarGroup.js')['useAvatarGroup']
  const useButtonGroup: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5.9.2__2f54f49be07767b799dc398325095cac/node_modules/@nuxt/ui/dist/runtime/composables/useButtonGroup.js')['useButtonGroup']
  const useComponentIcons: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5.9.2__2f54f49be07767b799dc398325095cac/node_modules/@nuxt/ui/dist/runtime/composables/useComponentIcons.js')['useComponentIcons']
  const useContentSearch: typeof import('./node_modules/.pnpm/@nuxt+ui-pro@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5._69fd9d08633dfe1d3c53c8f01b19e750/node_modules/@nuxt/ui-pro/dist/runtime/composables/useContentSearch.js')['useContentSearch']
  const useFileUpload: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5.9.2__2f54f49be07767b799dc398325095cac/node_modules/@nuxt/ui/dist/runtime/composables/useFileUpload.js')['useFileUpload']
  const useFormField: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5.9.2__2f54f49be07767b799dc398325095cac/node_modules/@nuxt/ui/dist/runtime/composables/useFormField.js')['useFormField']
  const useKbd: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5.9.2__2f54f49be07767b799dc398325095cac/node_modules/@nuxt/ui/dist/runtime/composables/useKbd.js')['useKbd']
  const useLocale: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5.9.2__2f54f49be07767b799dc398325095cac/node_modules/@nuxt/ui/dist/runtime/composables/useLocale.js')['useLocale']
  const useLocalePro: typeof import('./node_modules/.pnpm/@nuxt+ui-pro@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5._69fd9d08633dfe1d3c53c8f01b19e750/node_modules/@nuxt/ui-pro/dist/runtime/composables/useLocalePro.js')['useLocalePro']
  const useOverlay: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5.9.2__2f54f49be07767b799dc398325095cac/node_modules/@nuxt/ui/dist/runtime/composables/useOverlay.js')['useOverlay']
  const usePortal: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5.9.2__2f54f49be07767b799dc398325095cac/node_modules/@nuxt/ui/dist/runtime/composables/usePortal.js')['usePortal']
  const useResizable: typeof import('./node_modules/.pnpm/@nuxt+ui-pro@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5._69fd9d08633dfe1d3c53c8f01b19e750/node_modules/@nuxt/ui-pro/dist/runtime/composables/useResizable.js')['useResizable']
  const useScrollspy: typeof import('./node_modules/.pnpm/@nuxt+ui-pro@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5._69fd9d08633dfe1d3c53c8f01b19e750/node_modules/@nuxt/ui-pro/dist/runtime/composables/useScrollspy.js')['useScrollspy']
  const useToast: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5.9.2__2f54f49be07767b799dc398325095cac/node_modules/@nuxt/ui/dist/runtime/composables/useToast.js')['useToast']
}
// for type re-export
declare global {
  // @ts-ignore
  export type { UseResizableProps, UseResizableReturn } from './node_modules/.pnpm/@nuxt+ui-pro@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5._69fd9d08633dfe1d3c53c8f01b19e750/node_modules/@nuxt/ui-pro/dist/runtime/composables/useResizable.d'
  import('./node_modules/.pnpm/@nuxt+ui-pro@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5._69fd9d08633dfe1d3c53c8f01b19e750/node_modules/@nuxt/ui-pro/dist/runtime/composables/useResizable.d')
  // @ts-ignore
  export type { ShortcutConfig, ShortcutsConfig, ShortcutsOptions } from './node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5.9.2__2f54f49be07767b799dc398325095cac/node_modules/@nuxt/ui/dist/runtime/composables/defineShortcuts.d'
  import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5.9.2__2f54f49be07767b799dc398325095cac/node_modules/@nuxt/ui/dist/runtime/composables/defineShortcuts.d')
  // @ts-ignore
  export type { UseComponentIconsProps } from './node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5.9.2__2f54f49be07767b799dc398325095cac/node_modules/@nuxt/ui/dist/runtime/composables/useComponentIcons.d'
  import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5.9.2__2f54f49be07767b799dc398325095cac/node_modules/@nuxt/ui/dist/runtime/composables/useComponentIcons.d')
  // @ts-ignore
  export type { UseFileUploadOptions } from './node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5.9.2__2f54f49be07767b799dc398325095cac/node_modules/@nuxt/ui/dist/runtime/composables/useFileUpload.d'
  import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5.9.2__2f54f49be07767b799dc398325095cac/node_modules/@nuxt/ui/dist/runtime/composables/useFileUpload.d')
  // @ts-ignore
  export type { KbdKey, KbdKeySpecific } from './node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5.9.2__2f54f49be07767b799dc398325095cac/node_modules/@nuxt/ui/dist/runtime/composables/useKbd.d'
  import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5.9.2__2f54f49be07767b799dc398325095cac/node_modules/@nuxt/ui/dist/runtime/composables/useKbd.d')
  // @ts-ignore
  export type { OverlayOptions, Overlay } from './node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5.9.2__2f54f49be07767b799dc398325095cac/node_modules/@nuxt/ui/dist/runtime/composables/useOverlay.d'
  import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5.9.2__2f54f49be07767b799dc398325095cac/node_modules/@nuxt/ui/dist/runtime/composables/useOverlay.d')
  // @ts-ignore
  export type { Toast } from './node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5.9.2__2f54f49be07767b799dc398325095cac/node_modules/@nuxt/ui/dist/runtime/composables/useToast.d'
  import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_@inertiajs+vue3@2.0.17_vue@3.5.18_typescript@5.9.2__2f54f49be07767b799dc398325095cac/node_modules/@nuxt/ui/dist/runtime/composables/useToast.d')
}
