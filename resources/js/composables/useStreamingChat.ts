import { useChat } from '@ai-sdk/vue';
import { ref, computed } from 'vue';
import type { UserContext, ChatError } from '@/types';

export function useStreamingChat() {
    const userContext = ref<UserContext | null>(null);
    const chatError = ref<ChatError | null>(null);
    
    // Get CSRF token
    const getCsrfToken = (): string => {
        const token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        if (!token) {
            throw new Error('CSRF token not found');
        }
        return token;
    };

    // Configure the AI SDK useChat composable for our Laravel SSE endpoint
    const {
        messages,
        input,
        handleSubmit,
        isLoading,
        error,
        reload,
        stop,
        setMessages,
    } = useChat({
        api: '/api/prism/chat/stream',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': getCsrfToken(),
        },
        body: {
            context: computed(() => userContext.value),
        },
        onError: (error) => {
            console.error('Chat error:', error);
            chatError.value = {
                message: error.message || 'An error occurred while processing your request',
                type: 'stream',
            };
        },
        onFinish: (message) => {
            console.log('Message finished:', message);
            chatError.value = null;
        },
    });

    // Load user context from the backend
    const loadUserContext = async (): Promise<void> => {
        try {
            const response = await fetch('/api/prism/context', {
                headers: {
                    'X-CSRF-TOKEN': getCsrfToken(),
                    'X-Requested-With': 'XMLHttpRequest',
                },
            });
            
            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    userContext.value = data.context;
                }
            }
        } catch (err) {
            console.warn('Failed to load user context:', err);
        }
    };

    // Update user context
    const updateUserContext = async (updates: Partial<UserContext>): Promise<void> => {
        try {
            const response = await fetch('/api/prism/context', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': getCsrfToken(),
                    'X-Requested-With': 'XMLHttpRequest',
                },
                body: JSON.stringify(updates),
            });
            
            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    userContext.value = data.context;
                }
            }
        } catch (err) {
            console.warn('Failed to update user context:', err);
        }
    };

    // Clear error
    const clearError = () => {
        chatError.value = null;
    };

    // Clear messages
    const clearMessages = () => {
        setMessages([]);
        clearError();
    };

    // Send message (wrapper around handleSubmit)
    const sendMessage = async (message: string) => {
        if (!message.trim() || isLoading.value) return;
        
        input.value = message.trim();
        clearError();
        
        try {
            await handleSubmit();
        } catch (err) {
            console.error('Failed to send message:', err);
            if (err instanceof Error) {
                chatError.value = {
                    message: err.message,
                    type: 'network',
                };
            }
        }
    };

    // Computed properties
    const hasMessages = computed(() => messages.value.length > 0);
    const lastMessage = computed(() => messages.value[messages.value.length - 1]);
    const isStreaming = computed(() => isLoading.value);
    const combinedError = computed(() => chatError.value || (error.value ? {
        message: error.value.message,
        type: 'stream' as const,
    } : null));

    return {
        // AI SDK state
        messages,
        input,
        isLoading,
        error: combinedError,
        
        // Custom state
        userContext: computed(() => userContext.value),
        
        // Computed
        hasMessages,
        lastMessage,
        isStreaming,
        
        // Actions
        sendMessage,
        handleSubmit,
        reload,
        stop,
        clearMessages,
        loadUserContext,
        updateUserContext,
        clearError,
    };
}
